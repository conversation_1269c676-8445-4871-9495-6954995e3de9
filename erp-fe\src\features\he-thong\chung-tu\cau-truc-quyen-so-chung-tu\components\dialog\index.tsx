import { useState, useEffect, useCallback } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog, AritoForm } from '@/components/custom/arito';
import useInputTableRow from './InputTableTab/useInputTableRow';
import { AritoIcon } from '@/components/custom/arito/icon';
import { DonViCoSo, ChungTu } from '@/types/schemas';
import ConfirmDialog from '../ConfirmDialog';
import InputTableTab from './InputTableTab';
import { formSchema } from '../../schemas';
import SoHienTaiTab from './SoHienTaiTab';
import { FormMode } from '@/types/form';
import GeneralTab from './GeneralTab';
import BasicForm from './BasicForm';

// Interface for User data (matching backend structure with profile)
interface UserProfile {
  uuid: string;
  nickname?: string | null;
}

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name?: string;
  is_staff?: boolean;
  is_active?: boolean;
  date_joined?: string;
  profile: UserProfile;
  user_id_data: User;
}

interface FormDialogProps {
  mode: FormMode;
  open: boolean;
  onClose: () => void;

  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  onWatchButtonClick?: () => void;

  // Parent component's submit handler - updated to use new data structure
  onSubmit?: (data: any) => void;

  initialData?: any;
}

function FormDialog({
  mode,
  open,
  onClose,
  onAddButtonClick,
  onCopyButtonClick,
  onDeleteButtonClick,
  onEditButtonClick,
  onSubmit,
  initialData
}: FormDialogProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [donVi, setDonVi] = useState<DonViCoSo | null>(null);

  // Hàm tạo dòng mới tùy chỉnh cho bảng "Chứng từ & Người sử dụng"
  const createGiaBanRow = useCallback(() => {
    const newRow = {
      uuid: String(Math.random()),
      ma_ct: '', // Mã chứng từ để hiển thị
      ten_ct: '', // Tên chứng từ để hiển thị
      user_id: '', // UUID người sử dụng từ profile (internal)
      username: '', // Username để hiển thị
      first_name: '' // Tên người sử dụng để hiển thị
    };
    return newRow;
  }, []);

  // Hàm tạo dòng mới tùy chỉnh cho bảng "Số hiện tại"
  const createSoHienTaiRow = useCallback(() => {
    const newRow = {
      uuid: String(Math.random()),
      ngay: '', // Ngày
      so_hien_tai: '' // Số hiện tại
    };
    return newRow;
  }, []);

  // Sử dụng hook useInputTableRow cho tab "Chứng từ & Người sử dụng"
  const {
    rows: tableData,
    setRows, // Uncomment to use for setting table data from initialData
    handleRowClick,
    handleAddRow: originalHandleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange: originalHandleCellValueChange
  } = useInputTableRow<any>([], createGiaBanRow);

  const {
    rows: soHienTaiTableData,
    setRows: setSoHienTaiRows,
    handleRowClick: handleSoHienTaiRowClick,
    handleCellValueChange: originalHandleSoHienTaiCellValueChange
  } = useInputTableRow<any>([], createSoHienTaiRow);

  // Wrap handleAddRow to add logging
  const handleAddRow = useCallback(() => {
    originalHandleAddRow();
  }, [originalHandleAddRow]);

  // Wrap handleCellValueChange to add logging
  const handleCellValueChange = useCallback(
    (rowUuid: string, field: string, newValue: any) => {
      originalHandleCellValueChange(rowUuid, field, newValue);
    },
    [originalHandleCellValueChange]
  );

  // Wrap handlers for "Số hiện tại" tab
  const handleSoHienTaiCellValueChange = useCallback(
    (rowUuid: string, field: string, newValue: any) => {
      originalHandleSoHienTaiCellValueChange(rowUuid, field, newValue);
    },
    [originalHandleSoHienTaiCellValueChange]
  );

  // State for storing selected data following GeneralTab pattern
  const [selectedChungTuData, setSelectedChungTuData] = useState<Record<string, ChungTu>>({});
  const [selectedUserData, setSelectedUserData] = useState<Record<string, User>>({});

  // Callback handlers following GeneralTab pattern
  const handleChungTuSelect = useCallback((rowUuid: string, chungTu: ChungTu) => {
    setSelectedChungTuData(prev => {
      const updated = {
        ...prev,
        [rowUuid]: chungTu
      };
      return updated;
    });
  }, []);

  const handleUserSelect = useCallback((rowUuid: string, user: User) => {
    setSelectedUserData(prev => {
      const updated = {
        ...prev,
        [rowUuid]: user
      };

      return updated;
    });
  }, []);

  const handleSubmit = async (data: any) => {
    console.log('Form data>>>>>>>>>>>:', data);
    console.log('Table data>>>>>>>>>>>:', tableData);

    // Create combined list with ma_ct and username format for "Chứng từ & Người sử dụng" tab
    // Map to chi_tiet format for API submission
    const combinedList = tableData.map((row, index) => {
      const chungTu = selectedChungTuData[row.uuid];
      const user = selectedUserData[row.uuid];

      const baseItem = {
        line: index + 1,
        ma_ct: chungTu?.chung_tu_uuid || chungTu?.uuid || '',
        username: user?.username || row.username || '',
        user_id: user?.user_id_data?.profile?.uuid || user?.profile?.uuid || null
      };

      if (mode === 'edit' && chungTu?.uuid) {
        return {
          ...baseItem,
          uuid: chungTu.uuid
        };
      }

      return baseItem;
    });

    // Create combined list for "Số hiện tại" tab - map back to ngay_nk and i_so_ct_ht format
    const soHienTaiCombinedList = soHienTaiTableData.map(row => {
      return {
        ngay_nk: row.ngay || '',
        i_so_ct_ht: row.so_hien_tai || ''
      };
    });

    // Log the combined lists in requested format
    console.log('📋👥 List of ChungTu and User:', combinedList);

    try {
      if (onSubmit) {
        const formData = {
          ...data,
          unit_id: donVi?.uuid || data.unit_id || null,
          danh_sach_chung_tu: combinedList,
          ngay: soHienTaiCombinedList
        };
        await onSubmit(formData);
      } else {
        setError('Không thể lưu dữ liệu: Không có xử lý submit');
      }
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra');
    }
  };

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  // Effect to update form fields when initialData changes
  useEffect(() => {
    if (initialData) {
      if (initialData.unit_id_data) {
        setDonVi(initialData.unit_id_data);
      }

      // Process initialData.chi_tiet if available (map from API response)
      if (initialData.chi_tiet && Array.isArray(initialData.chi_tiet) && initialData.chi_tiet.length > 0) {
        // Convert chi_tiet array to the format expected by the table
        const tableRows = initialData.chi_tiet.map((item: any) => {
          // Create a new row with a unique UUID
          const newRow = createGiaBanRow();

          newRow.ma_ct = item.ma_ct || '';
          newRow.ten_ct = item.ten_ct || '';
          newRow.user_id = item.user_id || ''; // This will now be UUID from profile
          newRow.username = item.username || '';
          newRow.first_name = item.user_id_data?.nickname || item.user_id_data?.first_name || '';

          // Store the selected data for form submission (following GeneralTab pattern)
          if (item.ma_ct) {
            setSelectedChungTuData(prev => ({
              ...prev,
              [newRow.uuid]: item
            }));
          }

          if (item.username && item.user_id_data) {
            setSelectedUserData(prev => ({
              ...prev,
              [newRow.uuid]: item
            }));
          }

          return newRow;
        });

        // Set the table data
        if (tableRows.length > 0) {
          // Use the setRows function from useInputTableRow
          setRows(tableRows);
        }
      }

      // Process initialData.ngay array if available (map ngay_nk and i_so_ct_ht)
      if (
        initialData.ngay &&
        Array.isArray(initialData.ngay) &&
        initialData.ngay.length > 0
      ) {
        // Convert ngay array to the format expected by the table
        const soHienTaiTableRows = initialData.ngay.map((item: any) => {
          // Create a new row with a unique UUID
          const newRow = createSoHienTaiRow();

          // Map ngay_nk to ngay field and i_so_ct_ht to so_hien_tai field
          newRow.ngay = item.ngay_nk || '';
          newRow.so_hien_tai = item.i_so_ct_ht || '';

          return newRow;
        });

        // Set the table data for "Số hiện tại" tab
        if (soHienTaiTableRows.length > 0) {
          setSoHienTaiRows(soHienTaiTableRows);
        }
      }
    }
  }, [initialData, setRows, setSoHienTaiRows, createGiaBanRow, createSoHienTaiRow]);

  return (
    <>
      <AritoDialog
        open={open}
        onClose={onClose}
        title={mode === 'add' ? 'Mới' : mode === 'edit' ? 'Sửa' : 'Xem'}
        maxWidth='lg'
        disableBackdropClose={false}
        disableEscapeKeyDown={false}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={mode}
          hasAritoActionBar={false}
          schema={formSchema}
          onSubmit={data => {
            handleSubmit(data);
          }}
          initialData={initialData}
          className='w-[50vw]'
          headerFields={<BasicForm mode={mode} />}
          classNameBottomBar='relative w-full flex justify-end gap-2'
          tabs={[
            {
              id: '1',
              label: 'Thông tin chung',
              component: <GeneralTab mode={mode} donVi={donVi} setDonVi={setDonVi} />
            },
            {
              id: '3',
              label: 'Chứng từ & Người sử dụng',
              component: (
                <InputTableTab
                  mode={mode}
                  rows={tableData}
                  onRowClick={handleRowClick}
                  onAddRow={handleAddRow}
                  onDeleteRow={handleDeleteRow}
                  onCopyRow={handleCopyRow}
                  onPasteRow={handlePasteRow}
                  onMoveRow={handleMoveRow}
                  onCellValueChange={handleCellValueChange}
                  onChungTuSelect={handleChungTuSelect}
                  onUserSelect={handleUserSelect}
                />
              )
            },
            {
              id: '4',
              label: 'Số hiện tại',
              component: (
                <SoHienTaiTab
                  mode={mode}
                  rows={soHienTaiTableData}
                  onRowClick={handleSoHienTaiRowClick}
                  onCellValueChange={handleSoHienTaiCellValueChange}
                />
              )
            }
          ]}
          bottomBar={
            <BottomBar
              mode={mode}
              onAdd={onAddButtonClick}
              onEdit={onEditButtonClick}
              onDelete={onDeleteButtonClick}
              onCopy={onCopyButtonClick}
              onClose={onClose}
            />
          }
        />
        {error && <div className='mx-4 mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
      </AritoDialog>

      <ConfirmDialog
        onClose={handleCloseDialog}
        open={showConfirmDialog}
        onCloseConfirmDialog={() => setShowConfirmDialog(false)}
      />
    </>
  );
}

export default FormDialog;
